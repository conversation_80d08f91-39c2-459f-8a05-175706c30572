{"name": "website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H underscor.host -p 3030 --experimental-https --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@aws-sdk/client-s3": "^3.859.0", "@lordicon/react": "^1.11.0", "@payloadcms/db-postgres": "3.50.0", "@payloadcms/next": "3.50.0", "@payloadcms/richtext-lexical": "3.50.0", "@prisma/client": "^6.13.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@splinetool/react-spline": "^4.1.0", "@splinetool/runtime": "^1.10.41", "@vercel/analytics": "^1.5.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "cryptography": "^1.2.3", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "graphql": "^16.11.0", "highlight.js": "^11.11.1", "html-react-parser": "^5.2.6", "ldrs": "^1.1.7", "lucide-react": "^0.539.0", "next": "15.4.5", "next-auth": "5.0.0-beta.29", "next-themes": "^0.4.6", "payload": "3.50.0", "react": "19.1.0", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-html-parser": "^2.0.2", "react-icons": "^5.5.0", "react-quill-new": "^3.6.0", "react-redux": "^9.2.0", "react-resizable-panels": "^3.0.4", "react-type-animation": "^3.2.0", "redux-persist": "^6.0.0", "sharp": "^0.34.3", "sonner": "^2.0.7", "swiper": "^11.2.10", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^4.0.15"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "eslint": "^9.32.0", "eslint-config-next": "15.4.5", "prisma": "^6.13.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2"}}