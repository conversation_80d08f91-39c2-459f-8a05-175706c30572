import { NextRequest, NextResponse } from "next/server";
import { AuthService } from "@/lib/api/services/auth";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const authService = new AuthService();

    const result = await authService.register(
      body.name,
      body.email,
      body.password
    );

    if (result.success) {
      return NextResponse.json(
        {
          success: true,
          data: result.data,
          message: "Registration successful",
          statusCode: 200,
          timestamp: new Date().toISOString(),
        },
        { status: 200 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: result.message || result.error || "Registration failed",
        error: result.error, // Keep for backward compatibility
        statusCode: 400,
        timestamp: new Date().toISOString(),
      },
      { status: 400 }
    );
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        message: error.message || "Internal server error",
        error: error.message,
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
      }
    );
  }
}
