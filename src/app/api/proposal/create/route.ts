"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { ProposalService } from "@/lib/api/services/proposal";

export async function POST(request: NextRequest) {
  try {
    // Initialize proposal service
    const session: any = await auth();
    const proposalService = new ProposalService({
      requireAuth: true,
    });

    // Set the service context with session and request
    proposalService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Extract request body
    const body = await request.json();

    // Use the service to create proposal
    const result = await proposalService.createProposal(body);

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Proposal CREATE error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
