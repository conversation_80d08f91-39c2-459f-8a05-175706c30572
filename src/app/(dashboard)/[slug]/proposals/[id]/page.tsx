"use client";

import React, { Suspense, use } from "react";
import { ProposalDetailsContainer } from "@/components/view/proposals/details/container";

interface ProposalDetailsPageProps {
  params: {
    slug: string;
    id: string;
  };
}

export default function ProposalDetailsPage({ params }: Promise<any>) {
  let proposal: ProposalDetailsPageProps = use(params);
  return (
    <Suspense fallback={<div>Loading proposal details...</div>}>
      <ProposalDetailsContainer proposalId={proposal?.id} />
    </Suspense>
  );
}
