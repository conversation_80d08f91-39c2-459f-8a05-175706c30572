"use client";

import {
  Avatar,
  AvatarImage,
  AvatarFallback,
} from "@/components/common/ui/avatar";
import { Card, CardContent } from "@/components/common/ui/card";
import { Button } from "@/components/common/ui/button";
import {
  mockUserProfile,
  mockActivityData,
  mockWorkingFormat,
  mockTimeEntries,
  mockCalendarEvents,
  mockCompanyInfo,
  mockCalendarData,
} from "@/data/dashboard-mock";
import { Play, ChevronLeft, ChevronRight, Video } from "lucide-react";

export default function Page() {
  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Main Grid */}
        <div className="grid grid-cols-12 gap-6">
          {/* Left Column - Profile */}
          <div className="col-span-3">
            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="mb-6">
                  <Avatar className="w-32 h-32 mb-4">
                    <AvatarImage
                      src={mockUserProfile.avatar}
                      alt={mockUserProfile.name}
                    />
                    <AvatarFallback className="text-2xl">AC</AvatarFallback>
                  </Avatar>
                  <div className="text-sm text-zinc-400 mb-1">Name</div>
                  <div className="text-xl font-semibold mb-4">
                    {mockUserProfile.name}
                  </div>

                  <div className="text-sm text-zinc-400 mb-1">Position</div>
                  <div className="mb-4">{mockUserProfile.position}</div>

                  <div className="text-sm text-zinc-400 mb-1">Email</div>
                  <div className="mb-4 text-lime-400">
                    {mockUserProfile.email}
                  </div>

                  <div className="text-sm text-zinc-400 mb-1">Joined on</div>
                  <div className="mb-6">{mockUserProfile.joinedDate}</div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <div className="text-3xl font-bold">
                      {mockUserProfile.yearsInCompany}
                    </div>
                    <div className="text-sm text-zinc-400">years</div>
                    <div className="text-xs text-zinc-500 flex items-center gap-1 mt-1">
                      <div className="w-3 h-3 rounded-full border border-zinc-500"></div>
                      In company
                    </div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold">
                      ${mockUserProfile.salary.toLocaleString()}
                    </div>
                    <div className="text-sm text-zinc-400 flex items-center gap-1">
                      <div className="w-3 h-3 rounded-full border border-zinc-500"></div>
                      Salary
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-3xl font-bold">
                      {mockUserProfile.projectsInProgress}
                    </div>
                    <div className="text-sm text-zinc-400">projects</div>
                    <div className="text-xs text-zinc-500 flex items-center gap-1 mt-1">
                      <div className="w-3 h-3 rounded-full border border-zinc-500"></div>
                      In progress
                    </div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold">
                      {mockUserProfile.projectsCompleted}
                    </div>
                    <div className="text-sm text-zinc-400">projects</div>
                    <div className="text-xs text-zinc-500 flex items-center gap-1 mt-1">
                      <div className="w-3 h-3 rounded-full border border-zinc-500"></div>
                      Completed
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Middle Column - Activity & Time Tracking */}
          <div className="col-span-6 space-y-6">
            {/* Weekly Activity */}
            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center justify-center mb-6">
                  <div className="relative w-48 h-48">
                    <svg
                      className="w-full h-full transform -rotate-90"
                      viewBox="0 0 100 100"
                    >
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="none"
                        className="text-muted"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="none"
                        strokeDasharray={`${
                          mockActivityData.weeklyActivity * 2.51
                        } 251`}
                        className="text-lime-500"
                      />
                    </svg>
                    <div className="absolute inset-0 flex flex-col items-center justify-center">
                      <div className="text-4xl font-bold">
                        {mockActivityData.weeklyActivity}%
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Weekly activity
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex gap-1">
                        <div className="w-4 h-4 bg-lime-500 rounded"></div>
                        <div className="w-4 h-4 bg-lime-500 rounded"></div>
                        <div className="w-4 h-4 bg-lime-500 rounded"></div>
                      </div>
                      <span>Design</span>
                    </div>
                    <span className="font-semibold">
                      {mockActivityData.design}%
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex gap-1">
                        <div className="w-4 h-4 bg-zinc-600 rounded"></div>
                        <div className="w-4 h-4 bg-zinc-600 rounded"></div>
                        <div className="w-4 h-4 bg-zinc-600 rounded"></div>
                      </div>
                      <span>Communication</span>
                    </div>
                    <span className="font-semibold">
                      {mockActivityData.communication}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Time Tracking */}
            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold">Time tracking</h3>
                  <div className="text-sm text-muted-foreground">
                    Travel app
                  </div>
                </div>

                <div className="text-center mb-6">
                  <div className="text-3xl font-mono font-bold mb-2">
                    02:17:51
                  </div>
                  <Button variant="ghost" size="sm" className="text-green-500">
                    <Play className="w-4 h-4" />
                  </Button>
                </div>

                <div className="space-y-3">
                  {mockTimeEntries.map((entry) => (
                    <div
                      key={entry.id}
                      className="flex items-center justify-between p-3 bg-zinc-700 rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                          <div className="w-3 h-3 bg-zinc-900 rounded"></div>
                        </div>
                        <span>{entry.project}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-mono text-sm">{entry.time}</span>
                        <Button variant="ghost" size="sm">
                          <div className="w-2 h-2 bg-zinc-400 rounded-full"></div>
                          <div className="w-2 h-2 bg-zinc-400 rounded-full"></div>
                          <div className="w-2 h-2 bg-zinc-400 rounded-full"></div>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Working Format, Calendar & Schedule */}
          <div className="col-span-3 space-y-6">
            {/* Working Format */}
            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium">Working format</h3>
                  <span className="text-xs text-muted-foreground">Details</span>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-zinc-700 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold mb-1">
                      {mockWorkingFormat.remote}%
                    </div>
                    <div className="text-xs text-zinc-400">Remote</div>
                  </div>
                  <div className="bg-lime-100 text-zinc-900 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold mb-1">
                      {mockWorkingFormat.hybrid}%
                    </div>
                    <div className="text-xs text-zinc-600">Hybrid</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Calendar */}
            <Card className="bg-card border-border">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <Button variant="ghost" size="sm">
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <h3 className="font-medium">
                    {mockCalendarData.currentMonth}
                  </h3>
                  <Button variant="ghost" size="sm">
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-7 gap-1 mb-2">
                  {["M", "T", "W", "T", "F", "S", "S"].map((day, index) => (
                    <div
                      key={index}
                      className="text-center text-xs text-zinc-400 p-2"
                    >
                      {day}
                    </div>
                  ))}
                </div>

                <div className="grid grid-cols-7 gap-1">
                  {mockCalendarData.days.map((day, index) => (
                    <div
                      key={index}
                      className={`
                        text-center text-sm p-2 rounded cursor-pointer
                        ${day.isCurrentMonth ? "text-white" : "text-zinc-600"}
                        ${
                          day.isToday
                            ? "bg-lime-600 text-white"
                            : "hover:bg-zinc-700"
                        }
                      `}
                    >
                      {day.date}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
