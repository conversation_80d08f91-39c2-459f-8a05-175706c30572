"use client";

import React, { Suspense, use } from "react";
import { ContractDetailsContainer } from "@/components/view/contracts/details/container";

interface ContractDetailsPageProps {
  params: {
    slug: string;
    id: string;
  };
}

export default function ContractDetailsPage({ params }: Promise<any>) {
  let contract: ContractDetailsPageProps = use(params);
  return (
    <Suspense fallback={<div>Loading contract details...</div>}>
      <ContractDetailsContainer contractId={contract?.id} />
    </Suspense>
  );
}
