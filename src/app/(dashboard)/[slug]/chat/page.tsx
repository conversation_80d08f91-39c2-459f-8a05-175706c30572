"use client";

import React, { useEffect, useState, useRef } from "react";
import { useChat } from "@/hooks/useChat";
import { RoomList } from "@/components/view/chat/room-list";
import { MessageList } from "@/components/view/chat/message-list";
import { MessageComposer } from "@/components/view/chat/message-composer";
import { Button } from "@/components/common/ui/button";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import { Badge } from "@/components/common/ui/badge";
import {
  MessageCircle,
  Users,
  Settings,
  Phone,
  Video,
  MoreVertical,
  Hash,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  PanelResizeHandle as ResizableHandle,
  Panel as ResizablePanel,
  PanelGroup as ResizablePanelGroup,
} from "react-resizable-panels";

interface ChatPageProps {
  params: Promise<{ slug: string }>;
}

function ChatHeader({
  room,
  onlineMembers,
  onBackClick,
  showBackButton = false,
}: {
  room: any;
  onlineMembers: number;
  onBackClick?: () => void;
  showBackButton?: boolean;
}) {
  if (!room) {
    return (
      <div className="h-16 border-b bg-white flex items-center justify-center">
        <div className="text-gray-500">
          Select a conversation to start chatting
        </div>
      </div>
    );
  }

  return (
    <div className="h-16 border-b bg-white flex items-center justify-between px-4">
      <div className="flex items-center gap-3">
        {showBackButton && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 md:hidden"
            onClick={onBackClick}
          >
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </Button>
        )}
        <div className="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center">
          <Hash className="h-5 w-5 text-gray-600" />
        </div>
        <div>
          <h1 className="font-semibold text-gray-900">{room.name}</h1>
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Users className="h-3 w-3" />
            <span>{room.members?.length || 0} members</span>
            {onlineMembers > 0 && (
              <>
                <span>•</span>
                <div className="flex items-center gap-1">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <span>{onlineMembers} online</span>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <Phone className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <Video className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreVertical className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

function EmptyState() {
  return (
    <div className="flex-1 flex items-center justify-center bg-gray-50">
      <div className="text-center max-w-md">
        <div className="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <MessageCircle className="h-8 w-8 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Welcome to Chat
        </h3>
        <p className="text-gray-500 mb-4">
          Select a conversation from the sidebar to start chatting with your
          team members.
        </p>
        <div className="text-sm text-gray-400">
          💡 Tip: Use rich text formatting, share files, and collaborate in
          real-time
        </div>
      </div>
    </div>
  );
}

export default function ChatPage({ params }: ChatPageProps) {
  const {
    rooms,
    currentRoom,
    currentRoomId,
    messages,
    isLoading,
    isLoadingMessages,
    isSendingMessage,
    sendMessage,
    selectRoom,
    startTyping,
    stopTyping,
    currentUser,
  } = useChat();

  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [sidebarSize, setSidebarSize] = useState(25);
  const [isMobile, setIsMobile] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const panelGroupRef = useRef<any>(null);
  const resizeTimeoutRef = useRef<ReturnType<typeof setTimeout> | undefined>(
    undefined
  );

  // Save sidebar size to localStorage and detect mobile
  useEffect(() => {
    const savedSize = localStorage.getItem("chat-sidebar-size");
    if (savedSize) {
      setSidebarSize(parseInt(savedSize, 30));
    }

    // Mobile detection
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const handleSidebarResize = (size: number) => {
    setSidebarSize(size);
    localStorage.setItem("chat-sidebar-size", size.toString());
  };

  // Reset to default size
  const resetPanelSizes = () => {
    const defaultSize = 25;
    setSidebarSize(defaultSize);
    localStorage.setItem("chat-sidebar-size", defaultSize.toString());

    // Programmatically resize the panels
    if (panelGroupRef.current) {
      panelGroupRef.current.setLayout([defaultSize, 100 - defaultSize]);
    }
  };

  // Calculate online members for current room
  const onlineMembers =
    currentRoom?.members?.filter((m) => m.state === "online").length || 0;

  // Handle room selection
  const handleSelectRoom = (roomId: string) => {
    selectRoom(roomId);
  };

  // Handle send message
  const handleSendMessage = (content: string) => {
    if (currentRoomId) {
      sendMessage(content);
    }
  };

  // Handle typing indicators
  const handleStartTyping = () => {
    if (currentRoomId) {
      startTyping(currentRoomId);
    }
  };

  const handleStopTyping = () => {
    if (currentRoomId) {
      stopTyping(currentRoomId);
    }
  };

  // Handle back navigation on mobile
  const handleBackToRooms = () => {
    selectRoom(null);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Escape key to go back to rooms on mobile
      if (event.key === "Escape" && isMobile && currentRoom) {
        handleBackToRooms();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isMobile, currentRoom]);

  // Cleanup resize timeout on unmount
  useEffect(() => {
    return () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="h-screen bg-gray-50">
      <ResizablePanelGroup
        ref={panelGroupRef}
        direction="horizontal"
        className={cn("h-full", isResizing && "select-none")}
        onLayout={(sizes: number[]) => {
          if (sizes[0] && Math.abs(sizes[0] - sidebarSize) > 0.1) {
            // Set resizing state
            setIsResizing(true);

            // Clear existing timeout
            if (resizeTimeoutRef.current) {
              clearTimeout(resizeTimeoutRef.current);
            }

            // Update size
            handleSidebarResize(sizes[0]);

            // Stop resizing after a delay
            resizeTimeoutRef.current = setTimeout(() => {
              setIsResizing(false);
            }, 150);
          }
        }}
      >
        {/* Left sidebar - Room list */}
        <ResizablePanel
          defaultSize={isMobile ? 100 : sidebarSize}
          minSize={isMobile ? 100 : 20}
          maxSize={isMobile ? 100 : 40}
          className={cn(
            isMobile ? "min-w-full" : "min-w-[280px]",
            currentRoom && isMobile ? "hidden" : ""
          )}
        >
          <RoomList
            rooms={rooms}
            currentRoomId={currentRoomId}
            onSelectRoom={handleSelectRoom}
            onCreateRoom={() => setShowCreateRoom(true)}
            isLoading={isLoading}
            className="h-full"
          />
        </ResizablePanel>

        {!isMobile && (
          <ResizableHandle
            className="w-1 bg-gray-200 hover:bg-blue-400 active:bg-blue-500 transition-all duration-200 relative group cursor-col-resize"
            onDoubleClick={resetPanelSizes}
          >
            <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 bg-gray-300 group-hover:bg-blue-400 group-active:bg-blue-500 transition-colors duration-200" />

            {/* Tooltip on hover */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
              Double-click to reset
            </div>
            {/* Visual indicator when resizing */}
            {isResizing && (
              <>
                <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 bg-blue-500 shadow-lg" />
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-600 text-white text-xs px-2 py-1 rounded shadow-lg pointer-events-none">
                  {Math.round(sidebarSize)}%
                </div>
              </>
            )}
          </ResizableHandle>
        )}

        {/* Right side - Chat area */}
        <ResizablePanel
          defaultSize={isMobile ? 100 : 100 - sidebarSize}
          minSize={isMobile ? 100 : 60}
          className={cn(!currentRoom && isMobile ? "hidden" : "")}
        >
          <div className="flex flex-col h-full">
            {currentRoom ? (
              <>
                {/* Chat header */}
                <ChatHeader
                  room={currentRoom}
                  onlineMembers={onlineMembers}
                  onBackClick={handleBackToRooms}
                  showBackButton={isMobile}
                />

                {/* Messages area */}
                <MessageList
                  messages={messages}
                  currentUserId={currentUser?.id}
                  isLoading={isLoadingMessages}
                  className="flex-1"
                />

                {/* Message composer */}
                <MessageComposer
                  onSendMessage={handleSendMessage}
                  onStartTyping={handleStartTyping}
                  onStopTyping={handleStopTyping}
                  disabled={isSendingMessage}
                  placeholder={`Message ${currentRoom.name}...`}
                />
              </>
            ) : (
              <EmptyState />
            )}
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>

      {/* Create room modal would go here */}
      {showCreateRoom && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-lg font-semibold mb-4">
              Create New Conversation
            </h2>
            <p className="text-gray-500 mb-4">
              This feature will be implemented in the next phase.
            </p>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowCreateRoom(false)}
              >
                Cancel
              </Button>
              <Button onClick={() => setShowCreateRoom(false)}>OK</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
