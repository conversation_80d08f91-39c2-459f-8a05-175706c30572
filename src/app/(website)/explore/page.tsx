"use client";

import { useEffect, useState } from "react";

import useS<PERSON> from "swr";
import fetcher from "@/lib/common/requests";

import { Heading, News as Card, Carousel } from "@/components/common";

const API_KEY = "f71f1099e905749db4c7c4238c014611";
const NORMAL_NEWS_BASE_URL = `https://gnews.io/api/v4?apikey=${API_KEY}&lang=en&country=us`;
const BREAKING_NEWS_BASE_URL = `https://gnews.io/api/v4/top-headlines?apikey=${API_KEY}&lang=en&country=us`;

const Explore = () => {
  // This due to budget constraints where we cannot accommodate this API cost
  let [ifNewsIsAvailable, setIfNewsIsAvailable] = useState(false);
  let url = `${BREAKING_NEWS_BASE_URL}&q=breaking%20news&category=business&max=1`;

  let { data } = useSWR(url, fetcher);

  useEffect(() => {
    let { articles } = data ?? {};
    setIfNewsIsAvailable(articles?.length > 0);
  }, [data]);

  return (
    <section className="w-full h-full flex flex-col gap-12 justify-end items-center">
      {ifNewsIsAvailable && (
        <div className="w-full grid-cols-1 md:grid-cols-2 gap-12">
          <TopHeadLine />
          <LatestNews />
        </div>
      )}
      <div className="w-full flex flex-col gap-12">
        {ifNewsIsAvailable && <Articles />}
        <OurFeed />
        {ifNewsIsAvailable && <MoreNews />}
      </div>
    </section>
  );
};

const TopHeadLine = () => {
  let url = `${BREAKING_NEWS_BASE_URL}&q=breaking%20news&category=technology&max=1`;
  let { data } = useSWR(url, fetcher);

  if (!data?.articles?.length)
    return (
      <div>
        <p>No posts yet</p>
      </div>
    );

  let article = data?.articles?.[0] ?? {};

  return (
    <Container className="w-full h-full gap-12">
      <Heading title={"Headline news today"} />
      <Card type="large" {...article} />
    </Container>
  );
};

const LatestNews = () => {
  let url = `${BREAKING_NEWS_BASE_URL}&q=ai%20machine%20learning&category=business`;
  let { data } = useSWR(url, fetcher);

  if (!data?.articles?.length) return <></>;

  return (
    <Container className="w-full flex flex-col gap-12">
      <Heading title={"Latest news"} />
      <span className="w-full h-[50em] flex flex-col gap-8 overflow-y-scroll">
        {data?.articles?.map((article: any, index: number) => {
          return <Card key={index} type="small" {...article} />;
        })}
      </span>
    </Container>
  );
};

const Articles = () => {
  let url = `${NORMAL_NEWS_BASE_URL}&q=ai%20artificial%20intelligence&category=space`;
  let { data } = useSWR(url, fetcher);

  return (
    <Container className="w-full flex flex-col gap-8">
      <Heading title="Articles" />
      <div className="w-full max-w-[100vw] flex flex-row overflow-x-scroll gap-16">
        {data?.articles?.map((article: any, index: number) => {
          return <Card key={index} {...article} type="medium" />;
        })}
      </div>
    </Container>
  );
};

const OurFeed = () => {
  let { data } = useSWR(
    "https://feeds.behold.so/sklzOCEvPCxz4MgdX0An",
    fetcher
  );

  let pictures = data?.posts?.map((img: any) => img?.sizes?.large?.mediaUrl);

  if (!data?.posts?.length) return <></>;

  return (
    <Container>
      <Heading title="Our Feed" />
      <Carousel pictures={pictures} />
    </Container>
  );
};

const MoreNews = () => {
  let url = `${NORMAL_NEWS_BASE_URL}&q=ai%20machine%20learning&category=technology`;
  let { data } = useSWR(url, fetcher);

  return (
    <Container>
      <Heading title="More News" />
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-12">
        {data?.articles?.map((article: any, index: number) => {
          return <Card key={index} {...article} type="medium" />;
        })}
      </div>
    </Container>
  );
};

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
}

const Container = ({
  children,
  className = "w-full flex flex-col gap-8",
}: ContainerProps) => <div className={className}>{children}</div>;

export default Explore;
