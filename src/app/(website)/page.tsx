"use client";

import React from "react";

import Link from "next/link";
import { motion } from "framer-motion";
import { GridArt } from "@/components/common";
import { Section, Listing } from "@/modules";
import Pill from "@/components/common/ui/pill";

import { TypeAnimation } from "react-type-animation";
import {
  <PERSON><PERSON>Bt<PERSON>,
  Team as TeamCard,
  Standard as ProjectCard,
} from "@/components/common";

import DataWaveTheme from "@/modules/themes/datawave";

import {
  GiGrowth as GrowthIcon,
  GiElectric as ElectricIcon,
} from "react-icons/gi";
import { SiWritedotas as ProposeIcon } from "react-icons/si";
import { MdShield as ShieldIcon } from "react-icons/md";

// Request
import useSWR from "swr";
import fetcher from "@/lib/common/requests";

export default function Page() {
  return (
    <main className="w-full h-full">
      <GridArt amount={5} />
      <DataWaveTheme>
        <Hero />
      </DataWaveTheme>
      <Works />
      <Partnerships />
      {/* <Services /> */}
      <Aspirations />
      {/* <OurTeam /> */}
      <OurTechStack />
    </main>
  );
}

const Hero = () => {
  const transition = { ease: "easeInOut", duration: 200 };

  return (
    <motion.section
      initial={false}
      animate={{ opacity: 100 }}
      transition={transition}
      exit={{ opacity: 0 }}
      className="h-screen -bottom-10 flex flex-col gap-12 xl:gap-0 xl:flex-row xl:justify-between xl:items-end"
    >
      {/* Hero */}
      <span className="w-full flex flex-col gap-8 items-start justify-center">
        <motion.h6 className="text-white/50">Underscor</motion.h6>
        <motion.h1
          initial={false}
          animate={{ opacity: 100, transition }}
          transition={transition}
          className="w-max h-max inline-block text-white/50 leading-tight"
        >
          Your AI Enterprise <br />{" "}
          <TypeAnimation
            sequence={["Product", 4000, "Service", 4000, "Tool", 4000]}
            wrapper="span"
            style={{ color: "#25F4A1" }}
            speed={6}
            repeat={Infinity}
          />{" "}
          Architect
        </motion.h1>
        <div className="flex flex-row gap-3">
          <Pill>
            <GrowthIcon size={12} color={"#25F4A1"} />
            <p>Scalable</p>
          </Pill>
          <Pill>
            <ElectricIcon size={14} color={"#25F4A1"} /> <p>Fast</p>
          </Pill>
          <Pill>
            <ShieldIcon size={12} color={"#25F4A1"} />
            <p>Secure</p>
          </Pill>
        </div>
      </span>
      <div>
        <CtaBtns />
      </div>
    </motion.section>
  );
};

const Works = () => {
  return (
    <Section title="recent works">
      <Projects />
    </Section>
  );
};

const Projects = () => {
  const params = useSWR("projects?depth=1&drafts=false", fetcher);
  return <Listing request={params} grid={2} Component={ProjectCard} />;
};

const Partnerships = () => {
  return (
    <Section title="proudly in partnership with" position="center">
      <Partners />
    </Section>
  );
};

const Partners = () => {
  const data = [
    { index: 1, name: "vania", url: "https://vaniaconsultants.ae/" },
  ];

  const Partner = ({
    index,
    name,
    url,
  }: {
    name?: any;
    url?: any;
    index?: number;
  }) => {
    return (
      <Link
        href={url}
        className={
          // Exempt the last element
          index === data?.length
            ? ""
            : "border-b border-[#BACCD8] md:border-b-0 md:border-r mb-[50px] pb-[50px] md:mb-0 md:pb-0 md:mr-[100px] md:pr-[100px]"
        }
        target="blank"
      >
        <img
          className="partnership-logo saturation-reveal"
          src={`/svg/companies/${name}.svg`}
          width="auto"
          height="270px"
          alt={`underscor, ${name} affiliate`}
        />
      </Link>
    );
  };

  return (
    <Listing
      request={{ data, isLoading: false, error: false }}
      grid={1}
      Component={Partner}
    />
  );
};

const Services = () => {
  return (
    <Section title="our services">
      {/* Our services */}
      <h2 className="capitalize font-medium">
        Your <span className="text-lime-350">All-in-One</span> for: <br />{" "}
        Enterprise Resource Planning Solutions, Software as a service{" "}
        <span className="text-lime-350">{"(SaaS)"}</span>, Branding and
        Consultation Services
      </h2>

      {/* Welcoming */}
      <span className="w-full flex flex-col xl:flex-row gap-8 justify-between">
        {/* Salutations + img */}
        <span className="w-full max-w-md">
          <h6 className="capitalize">
            hi there, <br /> welcome to underscor{" "}
          </h6>
          {/* Block prism */}
          <img
            className="w-full"
            src="/img/prisms/block_slab.png"
            width="512"
            height="512"
            alt="underscor prism block slab"
          />
        </span>
        {/* Caption */}
        <span className="w-full flex flex-col gap-6 xl:gap-8">
          <h4>
            We are a team of passionate and innovative minds, dedicated to
            crafting enduring designs.
          </h4>
          <h4>
            Over the years, our unwavering commitment to excellence has drawn in
            a diverse clientele.
          </h4>
          <h4 className="text-pretty">
            With an arsenal enriched with fusion of expertise, boundless
            creativity, and cutting-edge technology, harmoniously converging to
            bring your brand to life in <br /> the ever-evolving digital
            landscape.
          </h4>
        </span>
      </span>
    </Section>
  );
};

const Aspirations = () => {
  return (
    <Section title="we aspire to work with">
      <Companies />
    </Section>
  );
};

const Companies = () => {
  const data = [
    { name: "adobe" },
    { name: "atlassian" },
    { name: "dropbox" },
    { name: "hubspot" },
    { name: "ibm" },
    { name: "intuit" },
    { name: "microsoft" },
    { name: "servicenow" },
    { name: "slack" },
    { name: "workday" },
    { name: "zendesk" },
    { name: "oracle" },
  ];

  const Company = ({ name }: { name?: string }) => {
    return (
      <Link
        href={`https://www.${name}.com`}
        className="company-logo-wrap"
        target="blank"
      >
        <img
          className="company-logo saturation-reveal"
          src={`/svg/companies/${name}.svg`}
          width="auto"
          height="30px"
          alt={`underscor, ${name} affiliate`}
        />
      </Link>
    );
  };

  return (
    <div className="marquee">
      <span className="track">
        <Listing
          request={{ data, isLoading: false, error: false }}
          className="flex flex-row gap-16"
          Component={Company}
        />
      </span>
    </div>
  );
};

const OurTeam = () => {
  return (
    <Section
      title="our team"
      theme="flex flex-col gap-24 bg-gradient-to-b from-black via-black"
    >
      <Team />
    </Section>
  );
};

const Team = () => {
  const data = [
    {
      img: "/img/team/alex.jpeg",
      name: "Alexander Swai",
      title: "founder | engineer",
      socials: [
        {
          url: "https://www.linkedin.com/in/alexander-cuthbert-swai",
          name: "linkedin",
        },
        { url: "https://github.com/alexthecurator", name: "github" },
      ],
    },
    {
      img: "/img/team/brian.jpeg",
      name: "Brian Mtungi",
      title: "designer",
      socials: [
        { url: "https://www.linkedin.com/in/mutungi", name: "linkedin" },
        { url: "https://dribbble.com/mtungi", name: "dribbble" },
      ],
    },
  ];

  return (
    <Listing
      request={{ data, isLoading: false, error: false }}
      grid={2}
      Component={TeamCard}
    />
  );
};

const OurTechStack = () => {
  return (
    <Section title="We built this using" theme="bg-transparent text-white">
      <TechStacks />
    </Section>
  );
};

const TechStacks = () => {
  const data = [
    { url: "https://react.dev", name: "react" },
    { url: "https://payloadcms.com", name: "payload" },
    { url: "https://vercel.com", name: "vercel" },
    { url: "https://vitejs.dev", name: "vite" },
    { url: "https://bun.sh", name: "bun" },
  ];

  const TechStack = ({ url, name }: { url: string; name?: string }) => {
    return (
      <Link href={url} target="blank">
        <img
          className="logo saturation-reveal"
          width="auto"
          height="100px"
          src={`/svg/techstack/${name}.svg`}
          alt={`${name} stack`}
        />
      </Link>
    );
  };

  return (
    <Listing
      request={{ data, isLoading: false, error: false }}
      className="flex flex-row gap-16 justify-center"
      Component={TechStack}
    />
  );
};
