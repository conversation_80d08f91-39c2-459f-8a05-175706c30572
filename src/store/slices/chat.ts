import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type {
  Room,
  Message,
  MemberWithState,
  UserState,
} from "@/lib/api/validators/schemas/chat";
import type { ChatStatistics } from "@/data/chat-mock";
import {
  fetchRooms,
  fetchMessages,
  sendMessage,
  createRoom,
  updateUserState,
  fetchTypingUsers,
  fetchChatStatistics,
} from "../actions/chat";

interface EnrichedRoom extends Room {
  members: MemberWithState[];
  lastMessage?: Message;
  unreadCount: number;
}

interface EnrichedMessage extends Message {
  sender?: {
    id: string;
    name: string;
    email: string;
    avatar: string;
  };
}

interface ChatState {
  rooms: EnrichedRoom[];
  currentRoomId: string | null;
  messages: Record<string, EnrichedMessage[]>; // roomId -> messages
  typingUsers: Record<string, any[]>; // roomId -> typing users
  userStates: Record<string, UserState>; // userId -> state
  statistics: ChatStatistics;
  isLoading: boolean;
  isLoadingMessages: boolean;
  isSendingMessage: boolean;
  isCreatingRoom: boolean;
  error: string | null;
}

const initialStatistics: ChatStatistics = {
  totalRooms: 0,
  totalMessages: 0,
  activeUsers: 0,
  onlineUsers: 0,
};

const initialState: ChatState = {
  rooms: [],
  currentRoomId: null,
  messages: {},
  typingUsers: {},
  userStates: {},
  statistics: initialStatistics,
  isLoading: false,
  isLoadingMessages: false,
  isSendingMessage: false,
  isCreatingRoom: false,
  error: null,
};

const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    // Set current room
    setCurrentRoom: (state, action: PayloadAction<string | null>) => {
      state.currentRoomId = action.payload;
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
    },

    // Add message optimistically (for real-time updates)
    addMessageOptimistic: (state, action: PayloadAction<EnrichedMessage>) => {
      const message = action.payload;
      if (message.roomId) {
        if (!state.messages[message.roomId]) {
          state.messages[message.roomId] = [];
        }
        state.messages[message.roomId].push(message);
        
        // Update room's last message and timestamp
        const roomIndex = state.rooms.findIndex(room => room.id === message.roomId);
        if (roomIndex !== -1) {
          state.rooms[roomIndex].lastMessage = message;
          state.rooms[roomIndex].updatedAt = message.createdAt;
        }
      }
    },

    // Update typing users for a room
    setTypingUsers: (state, action: PayloadAction<{ roomId: string; users: any[] }>) => {
      const { roomId, users } = action.payload;
      state.typingUsers[roomId] = users;
    },

    // Update user state
    setUserState: (state, action: PayloadAction<{ userId: string; userState: UserState }>) => {
      const { userId, userState } = action.payload;
      state.userStates[userId] = userState;
      
      // Update member states in rooms
      state.rooms.forEach(room => {
        const memberIndex = room.members.findIndex(member => member.accountId === userId);
        if (memberIndex !== -1) {
          room.members[memberIndex].state = userState;
          room.members[memberIndex].lastSeen = new Date();
        }
      });
    },

    // Mark messages as read
    markMessagesAsRead: (state, action: PayloadAction<string>) => {
      const roomId = action.payload;
      const roomIndex = state.rooms.findIndex(room => room.id === roomId);
      if (roomIndex !== -1) {
        state.rooms[roomIndex].unreadCount = 0;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch rooms
    builder
      .addCase(fetchRooms.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchRooms.fulfilled, (state, action) => {
        state.isLoading = false;
        state.rooms = action.payload;
      })
      .addCase(fetchRooms.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch messages
    builder
      .addCase(fetchMessages.pending, (state) => {
        state.isLoadingMessages = true;
        state.error = null;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.isLoadingMessages = false;
        const { roomId, messages } = action.payload;
        state.messages[roomId] = messages;
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.isLoadingMessages = false;
        state.error = action.payload as string;
      });

    // Send message
    builder
      .addCase(sendMessage.pending, (state) => {
        state.isSendingMessage = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isSendingMessage = false;
        const message = action.payload;
        
        // Add message to the appropriate room
        if (message.roomId) {
          if (!state.messages[message.roomId]) {
            state.messages[message.roomId] = [];
          }
          
          // Check if message already exists (to avoid duplicates from optimistic updates)
          const existingMessage = state.messages[message.roomId].find(m => m.id === message.id);
          if (!existingMessage) {
            state.messages[message.roomId].push(message);
          }
          
          // Update room's last message
          const roomIndex = state.rooms.findIndex(room => room.id === message.roomId);
          if (roomIndex !== -1) {
            state.rooms[roomIndex].lastMessage = message;
            state.rooms[roomIndex].updatedAt = message.createdAt;
          }
        }
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isSendingMessage = false;
        state.error = action.payload as string;
      });

    // Create room
    builder
      .addCase(createRoom.pending, (state) => {
        state.isCreatingRoom = true;
        state.error = null;
      })
      .addCase(createRoom.fulfilled, (state, action) => {
        state.isCreatingRoom = false;
        const newRoom = {
          ...action.payload,
          members: [],
          unreadCount: 0,
        };
        state.rooms.unshift(newRoom);
      })
      .addCase(createRoom.rejected, (state, action) => {
        state.isCreatingRoom = false;
        state.error = action.payload as string;
      });

    // Update user state
    builder
      .addCase(updateUserState.fulfilled, (state, action) => {
        const { userId, state: userState } = action.payload;
        state.userStates[userId] = userState;
        
        // Update member states in rooms
        state.rooms.forEach(room => {
          const memberIndex = room.members.findIndex(member => member.accountId === userId);
          if (memberIndex !== -1) {
            room.members[memberIndex].state = userState;
            room.members[memberIndex].lastSeen = new Date();
          }
        });
      });

    // Fetch typing users
    builder
      .addCase(fetchTypingUsers.fulfilled, (state, action) => {
        const { roomId, typingUsers } = action.payload;
        state.typingUsers[roomId] = typingUsers;
      });

    // Fetch statistics
    builder
      .addCase(fetchChatStatistics.fulfilled, (state, action) => {
        state.statistics = action.payload;
      });
  },
});

// Export actions
export const {
  setCurrentRoom,
  clearError,
  addMessageOptimistic,
  setTypingUsers,
  setUserState,
  markMessagesAsRead,
} = chatSlice.actions;

// Export reducer
export default chatSlice;
