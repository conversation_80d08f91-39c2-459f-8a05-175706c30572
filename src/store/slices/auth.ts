import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Session } from "next-auth";

// Define the auth state interface
export interface AuthState {
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  user: {
    id?: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  } | null;
}

// Initial state
const initialState: AuthState = {
  session: null,
  isAuthenticated: false,
  isLoading: true,
  user: null,
};

// Create the auth slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Set session data from next-auth
    setSession: (state, action: PayloadAction<Session | null>) => {
      state.session = action.payload;
      state.isAuthenticated = !!action.payload;
      state.isLoading = false;

      if (action.payload?.user) {
        state.user = {
          id: action.payload.user.id,
          name: action.payload.user.name,
          email: action.payload.user.email,
          image: action.payload.user.image,
        };
      } else {
        state.user = null;
      }
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Clear session (logout)
    clearSession: (state) => {
      state.session = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.user = null;
    },

    // Update user profile data
    updateUser: (state, action: PayloadAction<Partial<AuthState["user"]>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
});

// Export actions
export const { setSession, setLoading, clearSession, updateUser } =
  authSlice.actions;

// Export selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectSession = (state: { auth: AuthState }) => state.auth.session;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) =>
  state.auth.isAuthenticated;
export const selectIsLoading = (state: { auth: AuthState }) =>
  state.auth.isLoading;

// Export reducer
export default authSlice;
