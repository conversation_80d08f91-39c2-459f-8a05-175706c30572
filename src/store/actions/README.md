# Redux CRUD Actions for Proposals

This module provides comprehensive CRUD (Create, Read, Update, Delete) operations for proposals using Redux Toolkit async thunks.

## Features

- **Full CRUD Operations**: Create, Read, Update, Delete proposals
- **State Management**: Automatic state updates with loading states
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Type Safety**: Full TypeScript support with Zod schema validation
- **API Integration**: Direct communication with Next.js API routes

## API Endpoints

The actions communicate with the following API endpoints:

- `POST /api/proposal/create` - Create new proposal
- `GET /api/proposal/get` - Fetch proposals (with query parameters)
- `PUT /api/proposal/update` - Update existing proposal
- `DELETE /api/proposal/delete` - Delete proposal

## Available Actions

### Core CRUD Operations

```typescript
import { useProposals } from '@/hooks/useProposals';

function MyComponent() {
  const {
    create,
    fetchAll,
    fetchById,
    update,
    remove,
    proposals,
    isLoading,
    error
  } = useProposals();

  // Create a new proposal
  const handleCreate = async () => {
    await create({
      name: "New Proposal",
      description: "Proposal description",
      fixed_budget: 5000,
      total_budget: 10000,
      duration: 30,
      agreed_to_terms_and_conditions: true,
      accountId: "account-id"
    });
  };

  // Fetch all proposals
  const handleFetchAll = async () => {
    await fetchAll({ limit: 10, offset: 0 });
  };

  // Fetch single proposal
  const handleFetchById = async (id: string) => {
    await fetchById(id);
  };

  // Update proposal
  const handleUpdate = async (id: string) => {
    await update({
      id,
      name: "Updated Name",
      status: "inprogress"
    });
  };

  // Delete proposal
  const handleDelete = async (id: string) => {
    await remove(id);
  };
}
```

### State Management

The proposal slice manages the following state:

```typescript
interface ProposalState {
  proposals: Proposal[];           // Array of all proposals
  currentProposal: Proposal | null; // Currently selected proposal
  isLoading: boolean;              // General loading state
  isCreating: boolean;             // Creating operation state
  isUpdating: boolean;             // Updating operation state
  isDeleting: boolean;             // Deleting operation state
  error: string | null;            // Error message
  pagination: {                    // Pagination info
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  };
}
```

### Query Parameters

The `fetchProposals` action supports the following query parameters:

```typescript
interface ProposalQuery {
  id?: string;                     // Specific proposal ID
  status?: Status;                 // Filter by status
  accountId?: string;              // Filter by account
  limit?: number;                  // Results per page (max 100)
  offset?: number;                 // Pagination offset
  sortBy?: string;                 // Sort field
  sortOrder?: 'asc' | 'desc';      // Sort direction
}
```

## Usage Examples

### Basic List Component

```typescript
import { useProposals } from '@/hooks/useProposals';

export function ProposalList() {
  const { proposals, fetchAll, isLoading } = useProposals();

  useEffect(() => {
    fetchAll();
  }, []);

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      {proposals.map(proposal => (
        <div key={proposal.id}>
          <h3>{proposal.name}</h3>
          <p>{proposal.description}</p>
          <span>${proposal.total_budget}</span>
        </div>
      ))}
    </div>
  );
}
```

### Create Form Component

```typescript
import { useProposals } from '@/hooks/useProposals';

export function CreateProposalForm() {
  const { create, isCreating, error } = useProposals();

  const handleSubmit = async (formData: CreateProposal) => {
    try {
      await create(formData);
      // Handle success (e.g., redirect, show message)
    } catch (error) {
      // Error is automatically handled by Redux
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      <button type="submit" disabled={isCreating}>
        {isCreating ? 'Creating...' : 'Create Proposal'}
      </button>
      {error && <div className="error">{error}</div>}
    </form>
  );
}
```

## Error Handling

All actions include comprehensive error handling:

- Network errors are caught and displayed
- API errors are parsed and shown to users
- Loading states are properly managed
- Errors can be cleared manually

## Type Safety

All actions are fully typed using Zod schemas:

- `CreateProposal` - For creation operations
- `UpdateProposal` - For update operations
- `ProposalQuery` - For query parameters
- `Proposal` - For the complete proposal object

This ensures compile-time type checking and runtime validation.
