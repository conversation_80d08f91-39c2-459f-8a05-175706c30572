import { createAsyncThunk } from "@reduxjs/toolkit";
import { DocumentService } from "@/lib/api/services/document";
import type {
  Document,
  CreateDocument,
  UpdateDocument,
} from "@/lib/api/validators/schemas/document";
import type { DocumentStatistics } from "@/data/documents-mock";
import { toast } from "sonner";

const documentService = new DocumentService();

// Fetch all documents
export const fetchDocuments = createAsyncThunk(
  "documents/fetchDocuments",
  async (_, { rejectWithValue }) => {
    try {
      const response = await documentService.getDocuments();
      
      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch documents");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch documents",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch documents";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch single document
export const fetchDocument = createAsyncThunk(
  "documents/fetchDocument",
  async (documentId: string, { rejectWithValue }) => {
    try {
      const response = await documentService.getDocument(documentId);
      
      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch document");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch document",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch document";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create document
export const createDocument = createAsyncThunk(
  "documents/createDocument",
  async (documentData: CreateDocument, { rejectWithValue }) => {
    try {
      const response = await documentService.createDocument(documentData);
      
      if (response.success && !response.error) {
        toast.success(response.message || "Document created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create document");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create document",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create document";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update document
export const updateDocument = createAsyncThunk(
  "documents/updateDocument",
  async (
    { id, data }: { id: string; data: Partial<UpdateDocument> },
    { rejectWithValue }
  ) => {
    try {
      const response = await documentService.updateDocument(id, data);
      
      if (response.success && !response.error) {
        toast.success(response.message || "Document updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update document");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update document",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to update document";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Delete document
export const deleteDocument = createAsyncThunk(
  "documents/deleteDocument",
  async (documentId: string, { rejectWithValue }) => {
    try {
      const response = await documentService.deleteDocument(documentId);
      
      if (response.success && !response.error) {
        toast.success(response.message || "Document deleted successfully");
        return documentId;
      } else {
        toast.error(response.message || "Failed to delete document");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete document",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to delete document";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch document statistics
export const fetchDocumentStatistics = createAsyncThunk(
  "documents/fetchStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await documentService.getDocumentStatistics();
      
      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch document statistics");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch document statistics",
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch document statistics";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);
