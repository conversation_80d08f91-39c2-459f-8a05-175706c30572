"use client";

import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { RootState, AppDispatch } from "@/store";
import {
  fetchDocuments,
  fetchDocument,
  createDocument,
  updateDocument,
  deleteDocument,
  fetchDocumentStatistics,
} from "@/store/actions/documents";
import {
  clearError,
  setCurrentDocument,
  clearCurrentDocument,
} from "@/store/slices/document";
import type {
  Document,
  CreateDocument,
  UpdateDocument,
} from "@/lib/api/validators/schemas/document";

export function useDocuments() {
  const dispatch = useDispatch<AppDispatch>();

  const { documents, currentDocument, statistics, isLoading, error } =
    useSelector((state: RootState) => state.documents);

  // Fetch all documents
  const fetchAllDocuments = useCallback(async () => {
    try {
      await dispatch(fetchDocuments()).unwrap();
    } catch (error) {
      console.error("Failed to fetch documents:", error);
      throw error;
    }
  }, [dispatch]);

  // Fetch single document
  const fetchSingleDocument = useCallback(
    async (documentId: string) => {
      try {
        await dispatch(fetchDocument(documentId)).unwrap();
      } catch (error) {
        console.error("Failed to fetch document:", error);
        throw error;
      }
    },
    [dispatch]
  );

  // Create document
  const createNewDocument = useCallback(
    async (documentData: CreateDocument) => {
      try {
        await dispatch(createDocument(documentData)).unwrap();
      } catch (error) {
        console.error("Failed to create document:", error);
        throw error;
      }
    },
    [dispatch]
  );

  // Update document
  const updateExistingDocument = useCallback(
    async (id: string, documentData: Partial<UpdateDocument>) => {
      try {
        await dispatch(updateDocument({ id, data: documentData })).unwrap();
      } catch (error) {
        console.error("Failed to update document:", error);
        throw error;
      }
    },
    [dispatch]
  );

  // Delete document
  const deleteExistingDocument = useCallback(
    async (documentId: string) => {
      try {
        await dispatch(deleteDocument(documentId)).unwrap();
      } catch (error) {
        console.error("Failed to delete document:", error);
        throw error;
      }
    },
    [dispatch]
  );

  // Fetch document statistics
  const fetchStats = useCallback(async () => {
    try {
      await dispatch(fetchDocumentStatistics()).unwrap();
    } catch (error) {
      console.error("Failed to fetch document statistics:", error);
      throw error;
    }
  }, [dispatch]);

  // Clear error
  const clearDocumentError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Set current document
  const setDocument = useCallback(
    (document: Document | null) => {
      dispatch(setCurrentDocument(document));
    },
    [dispatch]
  );

  // Clear current document
  const clearDocument = useCallback(() => {
    dispatch(clearCurrentDocument());
  }, [dispatch]);

  // Auto-fetch documents and statistics on hook initialization
  const initializeDocuments = useCallback(async () => {
    try {
      await Promise.all([
        dispatch(fetchDocuments()).unwrap(),
        dispatch(fetchDocumentStatistics()).unwrap(),
      ]);
    } catch (error) {
      console.error("Failed to initialize documents:", error);
    }
  }, [dispatch]);

  return {
    // State
    documents,
    currentDocument,
    statistics,
    isLoading,
    error,

    // Actions
    fetchAllDocuments,
    fetchSingleDocument: fetchSingleDocument,
    fetchDocument: fetchSingleDocument, // Alias for consistency
    createDocument: createNewDocument,
    updateDocument: updateExistingDocument,
    deleteDocument: deleteExistingDocument,
    fetchDocumentStatistics: fetchStats,
    clearError: clearDocumentError,
    setCurrentDocument: setDocument,
    clearCurrentDocument: clearDocument,
    initializeDocuments,

    // Computed values
    hasDocuments: documents.length > 0,
    documentsCount: documents.length,
  };
}
