import { redirect } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import { useSession } from "next-auth/react";
import { RootState, AppDispatch } from "@/store";
import {
  refreshSession,
  loginUserWithOAuth,
  loginUserWithCredentials,
  registerUserWithCredentials,
  logoutUser,
} from "@/store/actions/auth";
import {
  selectAuth,
  selectSession,
  selectUser,
  selectIsAuthenticated,
  selectIsLoading,
  updateUser,
} from "@/store/slices/auth";

export function useAuth() {
  const dispatch = useDispatch<AppDispatch>();
  const { data: nextAuthSession } = useSession();

  // Redux selectors
  const auth = useSelector((state: RootState) => selectAuth(state));
  const session = useSelector((state: RootState) => selectSession(state));
  const user = useSelector((state: RootState) => selectUser(state));
  const isAuthenticated = useSelector((state: RootState) =>
    selectIsAuthenticated(state)
  );
  const isLoading = useSelector((state: RootState) => selectIsLoading(state));

  const registerUser = async (
    name: string,
    email: string,
    password: string
  ) => {
    try {
      const result = await dispatch(
        registerUserWithCredentials({ name, email, password })
      );
      return result;
    } catch (error) {
      console.error("Registration error:", error);
      throw error;
    }
  };

  // Auth actions
  const loginWithOAuth = async (provider: string = "google") => {
    try {
      await dispatch(loginUserWithOAuth(provider));
      redirect("/dashboard");
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  };

  const loginWithCredentials = async (email: string, password: string) => {
    try {
      await dispatch(loginUserWithCredentials({ email, password }));
      redirect("/dashboard");
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      const result = await dispatch(logoutUser());
      return result;
    } catch (error) {
      console.error("Logout error:", error);
      throw error;
    }
  };

  const refreshUserSession = async () => {
    try {
      const result = await dispatch(refreshSession());
      return result;
    } catch (error) {
      console.error("Refresh session error:", error);
      throw error;
    }
  };

  const updateUserProfile = (userData: Partial<typeof user>) => {
    dispatch(updateUser(userData));
  };

  return {
    // State
    auth,
    session,
    user,
    isAuthenticated,
    isLoading,
    nextAuthSession,

    // Actions
    registerUser,
    loginWithCredentials,
    loginWithOAuth,
    logout,
    refreshUserSession,
    updateUserProfile,
  };
}
