import cn from "clsx";

const Listing = ({
  grid = 0,
  request,
  className,
  Component,
}: ListingProps): React.ReactNode => {
  let { data, isLoading, error }: RequestParams = request ?? {};

  if (error) return <>Error</>;
  if (isLoading) return <>Loading...</>;
  if (!Component) return <>Please pass a component</>;
  if (!data?.length) return <></>;

  return (
    <div
      className={cn(
        "w-full",
        grid > 0 ? `grid grid-cols-${grid} gap-8` : "",
        className
      )}
    >
      {data?.map((item: any, index: number) => {
        return <Component key={index} {...item} />;
      })}
    </div>
  );
};

interface ListingProps {
  grid?: number;
  className?: string;
  request: RequestParams;
  Component: React.FC;
}

interface RequestParams {
  data: any[];
  isLoading: boolean;
  error: any;
}

export default Listing;
