"use client";

import { useState } from "react";

import { motion } from "framer-motion";
import { requests } from "@/lib/common/requests";

interface EmailData {
  interest: string[];
  product?: any[];
}

interface EmailProps {
  placeholder?: string;
  button?: string;
  theme?: "dark" | "light";
}

interface EmailSubscriberProps {
  data: EmailData;
  props: EmailProps;
}

const EmailSubscriber = ({ data, props }: EmailSubscriberProps) => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);

  const inputTheme = {
    dark: "border-white bg-transparent text-white",
    light: "border-black bg-transparent text-black",
  };

  const btnTheme = {
    dark: "lime",
    light: "navy",
  };

  async function submit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);

    try {
      const { api } = await import("@/lib/common/requests");

      await api.post("subscribe", {
        email,
        interest: data.interest,
        product: data.product,
      });

      setEmail("");
      alert("Subscribed successfully!");
    } catch (error) {
      alert("Error subscribing");
    } finally {
      setLoading(false);
    }
  }

  return (
    <form onSubmit={submit} className="w-full flex flex-col gap-4">
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder={props?.placeholder ?? "Enter your email"}
        required
        className={`border ${inputTheme[props?.theme ?? "light"]}`}
      />
      <button
        type="submit"
        disabled={loading}
        className={`solid ${btnTheme[props?.theme ?? "light"]} max-w-1/4`}
      >
        {loading ? "Subscribing..." : props?.button ?? "Subscribe"}
      </button>
    </form>
  );
};

export default EmailSubscriber;
