"use server";

import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";

// Create an S3 client
//
// You must copy the endpoint from your B2 bucket details
// and set the region to match.
const region = process.env.B2_REGION!;
const bucket = process.env.B2_BUCKET!;
const accessKeyId = process.env.B2_KEY_ID!;
const secretAccessKey = process.env.B2_APP_KEY!;

export const s3 = new S3Client({
  endpoint: `https://s3.${region}.backblazeb2.com`,
  region: region,
  credentials: {
    accessKeyId,
    secretAccessKey,
  },
});

export async function UploadToS3(location: string, file: File) {
  let key = location + "/" + file?.name + "." + file?.type;

  try {
    let response = await s3.send(
      new PutObjectCommand({
        Bucket: bucket,
        Key: key,
        Body: file.stream(),
      })
    );

    return response;
  } catch (err) {
    console.log("Error: ", err);
  }
}
