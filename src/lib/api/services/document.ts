import { BaseService } from "./base";
import {
  DocumentSchema,
  CreateDocumentSchema,
  UpdateDocumentSchema,
  type Document,
  type CreateDocument,
  type UpdateDocument,
} from "../validators/schemas/document";
import {
  mockDocuments,
  mockDocumentStatistics,
  type DocumentStatistics,
} from "@/data/documents-mock";

export class DocumentService extends BaseService {
  private documents: Document[] = [];

  constructor() {
    super();
    // Initialize with mock data
    this.initializeMockData();
  }

  private initializeMockData() {
    // Convert mock data to API format
    this.documents = mockDocuments.map((document) => ({
      id: document.id,
      name: document.name,
      path: document.path,
      file_type: document.file_type,
      size: document.size,
      status: document.status as Document["status"],
      category: document.category,
      association_entity: document.association_entity,
      association_id: document.association_id,
      proposalId: document.proposalId,
      createdAt: new Date(document.createdAt),
      updatedAt: new Date(document.updatedAt),
    }));
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get all documents
   */
  async getDocuments(): Promise<any> {
    try {
      this.log("info", "Fetching all documents");

      // Simulate API delay
      await this.delay(500);

      this.log("info", `Found ${this.documents.length} documents`);

      return this.createSuccessResponse(
        this.documents,
        200,
        "Documents retrieved successfully"
      );
    } catch (error) {
      this.log("error", `Error fetching documents: ${error}`);
      return this.createErrorResponse("Failed to fetch documents", 500);
    }
  }

  /**
   * Get document by ID
   */
  async getDocument(id: string): Promise<any> {
    try {
      this.log("info", `Fetching document with ID: ${id}`);

      // Simulate API delay
      await this.delay(300);

      const document = this.documents.find((d) => d.id === id);

      if (!document) {
        this.log("warn", `Document not found: ${id}`);
        return this.createErrorResponse("Document not found", 404);
      }

      this.log("info", `Document found: ${document.name}`);

      return this.createSuccessResponse(
        document,
        200,
        "Document retrieved successfully"
      );
    } catch (error) {
      this.log("error", `Error fetching document: ${error}`);
      return this.createErrorResponse("Failed to fetch document", 500);
    }
  }

  /**
   * Create a new document
   */
  async createDocument(documentData: CreateDocument): Promise<any> {
    try {
      this.log("info", "Creating new document", { name: documentData.name });

      // Validate input
      const validatedData = CreateDocumentSchema.parse(documentData);

      // Simulate API delay
      await this.delay(800);

      // Create new document
      const newDocument: Document = {
        id: `doc-${Date.now()}`,
        ...validatedData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Add to documents array
      this.documents.push(newDocument);

      this.log("info", `Document created: ${newDocument.name}`);

      return this.createSuccessResponse(
        newDocument,
        201,
        "Document created successfully"
      );
    } catch (error) {
      this.log("error", `Error creating document: ${error}`);
      return this.createErrorResponse("Failed to create document", 500);
    }
  }

  /**
   * Update an existing document
   */
  async updateDocument(
    id: string,
    documentData: Partial<UpdateDocument>
  ): Promise<any> {
    try {
      this.log("info", `Updating document: ${id}`, documentData);

      // Find existing document
      const existingDocumentIndex = this.documents.findIndex(
        (d) => d.id === id
      );

      if (existingDocumentIndex === -1) {
        this.log("warn", `Document not found for update: ${id}`);
        return this.createErrorResponse("Document not found", 404);
      }

      // Simulate API delay
      await this.delay(600);

      // Update document
      const updatedDocument = {
        ...this.documents[existingDocumentIndex],
        ...documentData,
        updatedAt: new Date(),
      };

      // Validate updated document
      const validatedDocument = DocumentSchema.parse(updatedDocument);

      // Update in array
      this.documents[existingDocumentIndex] = validatedDocument;

      this.log("info", `Document updated: ${validatedDocument.name}`);

      return this.createSuccessResponse(
        validatedDocument,
        200,
        "Document updated successfully"
      );
    } catch (error) {
      this.log("error", `Error updating document: ${error}`);
      return this.createErrorResponse("Failed to update document", 500);
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(documentId: string): Promise<any> {
    try {
      this.log("info", `Deleting document: ${documentId}`);

      // Find existing document
      const existingDocumentIndex = this.documents.findIndex(
        (d) => d.id === documentId
      );

      if (existingDocumentIndex === -1) {
        this.log("warn", `Document not found for deletion: ${documentId}`);
        return this.createErrorResponse("Document not found", 404);
      }

      // Simulate API delay
      await this.delay(400);

      // Remove from array
      const deletedDocument = this.documents.splice(
        existingDocumentIndex,
        1
      )[0];

      this.log("info", `Document deleted: ${deletedDocument.name}`);

      return this.createSuccessResponse(
        null,
        200,
        "Document deleted successfully"
      );
    } catch (error) {
      this.log("error", `Error deleting document: ${error}`);
      return this.createErrorResponse("Failed to delete document", 500);
    }
  }

  /**
   * Get document statistics
   */
  async getDocumentStatistics(): Promise<any> {
    try {
      this.log("info", "Fetching document statistics");

      // Simulate API delay
      await this.delay(300);

      // Calculate statistics from current documents
      const statistics = {
        ...mockDocumentStatistics,
        total: this.documents.length,
        created: this.documents.filter((d) => d.status === "created").length,
        submitted: this.documents.filter((d) => d.status === "submitted")
          .length,
        received: this.documents.filter((d) => d.status === "received").length,
        negotiating: this.documents.filter((d) => d.status === "negotiating")
          .length,
        agreed: this.documents.filter((d) => d.status === "agreed").length,
        inprogress: this.documents.filter((d) => d.status === "inprogress")
          .length,
        reviewing: this.documents.filter((d) => d.status === "reviewing")
          .length,
        completed: this.documents.filter((d) => d.status === "completed")
          .length,
      };

      this.log("info", "Document statistics calculated", statistics);

      return this.createSuccessResponse(
        statistics,
        200,
        "Document statistics retrieved successfully"
      );
    } catch (error) {
      this.log("error", `Error fetching document statistics: ${error}`);
      return this.createErrorResponse(
        "Failed to fetch document statistics",
        500
      );
    }
  }
}
