"use client";

import { ReactNode } from "react";
import { AuthProvider } from "./auth";
import { ReduxProvider } from "./redux";
import { UserInterfaceProvider } from "./context";
import { ThemeProvider } from "./theme";

interface ProvidersProps {
  children: ReactNode;
  session?: any;
}

export function Providers({ children, session }: ProvidersProps) {
  return (
    <ThemeProvider defaultTheme="system" storageKey="underscore-theme">
      <AuthProvider session={session}>
        <ReduxProvider>
          <UserInterfaceProvider>{children}</UserInterfaceProvider>
        </ReduxProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

// Export individual providers for flexibility
export { AuthProvider } from "./auth";
export { ReduxProvider } from "./redux";
export { UserInterfaceProvider } from "./context";
export { ThemeProvider } from "./theme";
