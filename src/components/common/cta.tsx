"use client";

import Link from "next/link";

// Icons
import { BiLogoUpwork as Upwork } from "react-icons/bi";
import { SiGooglemeet as GoogleMeet } from "react-icons/si";
import { ImQuill as Propose } from "react-icons/im";

import type { CtaBtnsProps } from "./types";

export const CtaBtns = ({ theme = "dark" }: CtaBtnsProps) => {
  const Style =
    "w-full px-8 py-4 gap-20 flex flex-row items-center justify-between";

  return (
    <span className="w-max flex flex-col justify-start items-end">
      <Link
        href="/signin"
        target="_blank"
        className={`${Style} solid ${theme != "dark" ? "black" : "white"}`}
      >
        <Propose size={22} /> <span>Propose a solution</span>
      </Link>
      <span className="w-full h-max flex flex-row items-center justify-center">
        <Link
          href="https://calendly.com/underscor/30min"
          target="_blank"
          className={`${Style} outline ${theme != "dark" ? "black" : "white"}`}
          style={{
            borderTop: "0px",
          }}
        >
          <GoogleMeet size={20} />{" "}
          <span className="text-nowrap">Let's Talk</span>
        </Link>
        <Link
          href="https://www.upwork.com/agencies/1655373505334222848"
          target="_blank"
          className={`${Style} ${theme != "dark" ? "black" : "white"}`}
        >
          <Upwork size={27} /> <span className="text-nowrap">Contract us</span>
        </Link>
      </span>
    </span>
  );
};
