# Theme System Documentation

## Overview

The theme system provides light/dark mode support with automatic system preference detection.

## Components

### ThemeProvider

Located in `src/providers/theme.tsx`

- Manages theme state and persistence
- Supports "light", "dark", and "system" modes
- Automatically applies theme classes to document root
- Persists theme preference in localStorage

### ThemeToggle

Located in `src/components/ui/theme-toggle.tsx`

- Full theme selector with dialog (Light/Dark/System options)
- Simple toggle button (just switches between light/dark)

## Usage

### Basic Setup

The ThemeProvider is already integrated in `src/providers/index.tsx`:

```tsx
<ThemeProvider defaultTheme="system" storageKey="underscore-theme">
  {/* Your app */}
</ThemeProvider>
```

### Using the Theme Toggle

```tsx
import { ThemeToggle, SimpleThemeToggle } from "@/components/common/ui/theme-toggle";

// Full theme selector with dialog
<ThemeToggle />

// Simple toggle button
<SimpleThemeToggle />
```

### Using the Theme Hook

```tsx
import { useTheme } from "@/providers/theme";

function MyComponent() {
  const { theme, setTheme, actualTheme } = useTheme();

  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Actual theme: {actualTheme}</p>
      <button onClick={() => setTheme("dark")}>Dark Mode</button>
    </div>
  );
}
```

## Theme-Aware Styling

Use Tailwind's theme-aware classes:

```tsx
// Instead of fixed colors
<div className="bg-white text-black dark:bg-gray-900 dark:text-white">

// Use semantic color classes
<div className="bg-background text-foreground">

// Available semantic classes:
// - bg-background / text-foreground
// - bg-card / text-card-foreground
// - bg-muted / text-muted-foreground
// - border-border
// - bg-primary / text-primary-foreground
// - bg-secondary / text-secondary-foreground
```

## CSS Variables

The theme system uses CSS custom properties defined in `src/app/globals.css`:

```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  /* ... more variables */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  /* ... more variables */
}
```

## Integration Points

- Dashboard header: Theme toggle in top-right corner
- All proposal components: Updated to use theme-aware colors
- Dashboard home page: Updated card backgrounds and text colors
