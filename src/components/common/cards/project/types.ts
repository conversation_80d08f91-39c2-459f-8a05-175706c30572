export interface ProjectImage {
  url?: string;
}

export interface ProjectLink {
  url: string;
  label: string;
}

export interface ProjectCategory {
  name: string;
}

export interface StandardProps {
  id?: string | number;
  image?: ProjectImage;
  title?: string;
  caption?: string;
  date?: string;
  style?: string;
  links?: ProjectLink[];
  categories?: ProjectCategory[];
}

export interface ThumbnailedProps {
  image?: ProjectImage;
  title?: string;
  caption?: string;
  navigateTo?: {
    internal?: boolean;
    url: string;
    params?: any;
  };
  style?: string;
}
