"use client";

import { motion } from "framer-motion";
import { useEffect, useState } from "react";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { RxArrowTopRight as ExtLink } from "react-icons/rx";

import { PiCircleDashed as DashCircleIcon } from "react-icons/pi";

import { removeSpaces } from "@/lib/common/utils";
import type { StandardProps } from "./types";

export const Standard = ({
  id,
  image,
  title,
  caption,
  date,
  style = "",
  links = [],
  categories = [],
}: StandardProps) => {
  const navigate = useRouter();
  const [hover, setHover] = useState(false);
  const [year, setYear] = useState("2024");

  const transition = {
    duration: 0.7,
    ease: "easeInOut",
  };

  // Extract Year from Date
  useEffect(() => {
    const datetime = new Date(date);
    setYear(datetime.getFullYear().toString());
  }, [date]);

  return (
    <span
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      //
      className={`w-full flex flex-col gap-8 ${style} cursor-pointer`}
    >
      {/* Image */}
      <span
        onClick={() => {
          navigate.push(`/projects/${id}`);
        }}
        //
        className={`w-full relative ${
          image?.url
            ? "bg-gradient-to-b from-lime-350/[.2] to-lime-300/[.8]"
            : "bg-lime-450 animate-pulse"
        } overflow-hidden`}
      >
        <motion.img
          animate={hover ? "skew" : "unskew"}
          transition={transition}
          variants={{
            skew: {
              perspective: 800,
              skewY: 1.2,
              rotateY: "12.6deg",
            },
            unskew: {
              perspective: 800,
              skewY: 0,
              rotateY: "0deg",
            },
          }}
          //
          className="w-full h-full origin-right object-cover"
          //
          src={(window.location.origin ?? "") + (image?.url ?? "")}
          width={"7680"}
          height={"4320"}
          alt={`underscore's ${title} card`}
        />
        <TapMe active={hover} />
      </span>
      <span className="w-full flex flex-col gap-4 md:flex-row justify-between items-center duration-500">
        <span className="w-full flex flex-col items-start gap-4 md:gap-6 xl:gap-8">
          {/* title */}
          <span
            onClick={() => {
              navigate.push(`/project`);
            }}
            className={`-gap-[2px] ${
              title ? "" : "bg-lime-450 animate-pulse flex w-[70%] h-8"
            }`}
          >
            <h6 className={hover ? "text-lime-250" : "text-white"}>{title}</h6>
            {/* underline */}
            <motion.span
              animate={title && title.length > 0 && hover ? "show" : "hide"}
              transition={transition}
              variants={{
                show: {
                  width: "100%",
                },
                hide: {
                  width: 0,
                },
              }}
              className={`h-[2px] flex bg-lime-200`}
            ></motion.span>
          </span>
          {/* Caption */}
          <p
            className={`${
              caption ? "" : "bg-lime-450 animate-pulse flex w-full h-28"
            } regular-para2 md:max-w-xl lg:max-w-3xl xl:max-w-5xl ${
              hover ? "text-lime-100" : "text-navy-50"
            }`}
          >
            {caption}
          </p>
          {/* Links */}
          <span className="w-full flex flex-row justify-start items-center gap-12">
            {links.map((link, index) => {
              const { url, label } = link ?? {};
              return (
                label && (
                  <Link
                    key={index}
                    href={url}
                    target="_blank"
                    className="flex flex-row items-center justify-center gap-2"
                  >
                    <span className="mb-[2.8px]">{label}</span>
                    <ExtLink size={18} />
                  </Link>
                )
              );
            })}
          </span>
        </span>
        {/* Categories */}
        <span className="w-full md:w-max h-full flex flex-row justify-between md:flex-col md:justify-between md:gap-8">
          <h6>{year}</h6>
          <span className="w-max flex flex-col items-start gap-1">
            {categories?.map((category, index) => {
              return (
                <p key={index} className="capitalize text-navy-100">
                  {category?.name}
                </p>
              );
            })}
          </span>
        </span>
        {/*  */}
      </span>
    </span>
  );
};

export const TapMe = ({ active = false, text = "Tap me" }) => {
  return (
    <motion.span
      animate={active ? "show" : "hide"}
      transition={{ ease: "backInOut", duration: 0.6 }}
      variants={{
        show: {
          left: "1px",
          rotate: "0deg",
        },
        hide: {
          left: 0,
          rotate: "180deg",
        },
      }}
      //
      className="w-max z-20 flex flex-row items-center justify-center gap-4 origin-left rounded-full border border-slate-700 bg-black absolute bottom-8 text-white py-1 pr-4 pl-2"
    >
      <DashCircleIcon size={18} />
      <p className="font-medium">
        {text} <span className="ml-1 animate-pulse">👆🏻</span>
      </p>
    </motion.span>
  );
};
