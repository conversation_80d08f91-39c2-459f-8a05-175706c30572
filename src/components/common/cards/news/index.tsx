import type { NewsProps } from "./types";

export let News = ({
  url,
  type = "full",
  date,
  image,
  title,
  description,
  button,
}: NewsProps) => {
  let Source = () => {
    return (
      <span className="flex flex-row gap-1">
        <span className="bg-lime-350 px-2">Source</span>
        <small>{date}</small>
      </span>
    );
  };

  //

  let Image = ({ size = "100px" }) => {
    return (
      <span className="w-max h-full flex flex-row overflow-hidden">
        <img
          className={`w-full max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl 2xl:max-w-2xl origin-right object-cover`}
          src={image}
          alt={`${title}-news-card`}
        />
      </span>
    );
  };

  switch (type) {
    case "x-large":
      return (
        <span
          className="w-full flex flex-row justify-between cursor-pointer"
          onClick={() => {
            window.open(url, "_blank");
          }}
        >
          {image && <Image size="30em" />}
        </span>
      );

    case "large":
      return (
        <span
          className="w-full flex flex-col gap-4 cursor-pointer"
          onClick={() => {
            window.open(url, "_blank");
          }}
        >
          {image && <Image size="800px" />}
          <span className="w-full flex flex-col gap-2">
            <Source />
            {title && <h4>{title}</h4>}
            {description && <p>{description}</p>}
            {button && <button>read more</button>}
          </span>
        </span>
      );

    case "medium":
      return (
        <span
          className="w-full flex flex-col md:flex-row gap-10 md:gap-10 cursor-pointer"
          onClick={() => {
            window.open(url, "_blank");
          }}
        >
          {image && <Image size="340px" />}
          <span className="w-full flex flex-col gap-2">
            <Source />
            {title && <h6>{title.substring(0, 64)}...</h6>}
            {description && <p>{description}</p>}
            {button && <button>read more</button>}
          </span>
        </span>
      );

    case "small":
      return (
        <span
          className="w-full flex flex-row gap-10 justify-between cursor-pointer"
          onClick={() => {
            window.open(url, "_blank");
          }}
        >
          <span className="w-full max-w-[50%] flex flex-col gap-4">
            <Source />
            {title && <p className="text-md font-bold">{title}</p>}
          </span>
          {image && <Image size="150px" />}
        </span>
      );
  }
};
