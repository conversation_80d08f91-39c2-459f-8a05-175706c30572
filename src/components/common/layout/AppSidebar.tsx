"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { FaHome, FaFileAlt, FaFolder, Fa<PERSON>ser, FaCog } from "react-icons/fa";
import { BsFillChatRightQuoteFill as ChatIcon } from "react-icons/bs";
import { RiContractFill as ContractsIcon } from "react-icons/ri";
import { ImExit } from "react-icons/im";

import { useAuth } from "@/hooks/useAuth";
import { useParams } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from "@/components/common/ui/sidebar";

// Navigation items
const navigationItems = [
  {
    title: "Contracts",
    url: "contracts",
    icon: ContractsIcon,
  },
  {
    title: "Proposals",
    url: "proposals",
    icon: FaFileAlt,
  },
  {
    title: "Documents",
    url: "documents",
    icon: FaFolder,
  },
];

// Footer items
const footerItems = [
  {
    title: "Settings",
    url: "/settings",
    icon: FaCog,
  },
  {
    title: "Exit",
    url: "/",
    icon: ImExit,
  },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();
  let { slug } = useParams();
  const { user, isAuthenticated } = useAuth();

  const handleRouting = (url: string) => "/" + slug + "/" + url;

  // Get user initials for avatar fallback
  const getUserInitials = (name?: string | null, email?: string) => {
    if (name) {
      return name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email[0].toUpperCase();
    }
    return "U";
  };

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            <FaFolder className="size-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">Underscore</span>
            <span className="truncate text-xs">Project Manager</span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={false}
                  tooltip={"Dashboard View"}
                >
                  <Link href={handleRouting("/")}>
                    <FaHome className="size-2" />
                    <p className="text-sm">Home</p>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarSeparator />

              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={true}
                  tooltip={"Conversations"}
                >
                  <Link href={handleRouting("chats")}>
                    <ChatIcon className="size-2" />
                    <p className="text-sm">Chats</p>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarSeparator />
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={pathname === item.url}
                    tooltip={item.title}
                  >
                    <Link href={handleRouting(item.url)}>
                      <item.icon className="size-2" />
                      <p className="text-sm">{item.title}</p>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarSeparator />

        {/* Settings */}
        <SidebarMenu>
          {footerItems.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                asChild
                isActive={pathname === item.url}
                tooltip={item.title}
              >
                <Link href={item.url}>
                  <item.icon className="size-2" />
                  <p className="text-sm">{item.title}</p>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>

        <SidebarSeparator />

        {/* Profile Section */}
        {isAuthenticated && user ? (
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                asChild
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <Link href="/profile">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage
                      src={user.image || undefined}
                      alt={user.name || "User"}
                    />
                    <AvatarFallback className="rounded-lg">
                      {getUserInitials(user.name, user.email)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {user.name || "User"}
                    </span>
                    <span className="truncate text-xs">{user.email}</span>
                  </div>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        ) : (
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild size="lg">
                <Link href="/signin">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarFallback className="rounded-lg">
                      <FaUser className="size-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">Sign In</span>
                    <span className="truncate text-xs">
                      Access your account
                    </span>
                  </div>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        )}
      </SidebarFooter>
    </Sidebar>
  );
}
