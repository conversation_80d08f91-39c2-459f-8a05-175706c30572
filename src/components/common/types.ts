// Art Component Types
export interface GridArtProps {
  amount?: number;
}

// CTA Component Types
export interface CtaBtnsProps {
  theme?: "dark" | "light";
}

// Footer Component Types
export interface FooterProps {
  // No props currently needed
}

export interface ContactItem {
  title: string;
  email: string;
  url: string;
}

// Tab Component Types
export interface TabStyle {
  width?: string;
  theme?: "lime" | "navy" | "outline";
}

export interface ListTabProps {
  title?: string;
  caption?: string;
  icon?: string;
  style?: TabStyle;
  position?: "start" | "center" | "end";
}

export interface RowTabProps {
  title?: string;
  caption?: string;
  style?: "lime" | "blue" | "navy";
}

// Loader Component Types
export interface LoaderProps {
  active: boolean;
}

// Scroll Component Types
export interface ScrollAnimationProps {
  // No props currently needed
}

// Logo Component Types
export interface BrandProps {
  size?: "lg" | "md" | "sm";
  style?: string;
}

// Heading Component Types
export interface HeadingProps {
  tagline?: string;
  title?: string;
}
