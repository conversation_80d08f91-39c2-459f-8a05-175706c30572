"use client";

import { FaGoogle, FaLinkedin } from "react-icons/fa";
import { useAuth } from "@/hooks/useAuth";

export function AuthButton() {
  const { user, isAuthenticated, isLoading, login, logout } = useAuth();

  if (isLoading) {
    return (
      <button disabled className="px-4 py-2 bg-gray-300 text-gray-500 rounded">
        Loading...
      </button>
    );
  }

  if (isAuthenticated && user) {
    return (
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          {user.image && (
            <img
              src={user.image}
              alt={user.name || "User"}
              className="w-8 h-8 rounded-full"
            />
          )}
          <span className="text-sm font-medium">{user.name || user.email}</span>
        </div>
        <button
          onClick={() => logout()}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Sign Out
        </button>
      </div>
    );
  }

  return (
    <div className="flex gap-2">
      <button
        onClick={() => login("google")}
        className="flex items-center px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        <FaGoogle className="mr-2 h-4 w-4" />
        Sign in with Google
      </button>
      <button
        onClick={() => login("linkedin")}
        className="flex items-center px-4 py-2 bg-blue-700 text-white rounded hover:bg-blue-800"
      >
        <FaLinkedin className="mr-2 h-4 w-4" />
        Sign in with LinkedIn
      </button>
    </div>
  );
}
