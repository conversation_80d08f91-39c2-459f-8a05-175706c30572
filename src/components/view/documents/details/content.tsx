"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/common/ui/card";
import { Badge } from "@/components/common/ui/badge";
import { Separator } from "@/components/common/ui/separator";
import {
  FileText,
  Calendar,
  Tag,
  Link as LinkIcon,
  User,
  Clock,
  CheckCircle,
} from "lucide-react";
import { format } from "date-fns";
import type { Document } from "@/lib/api/validators/schemas/document";

interface DocumentDetailsContentProps {
  document: Document;
}

const getStatusIcon = (status: Document["status"]) => {
  switch (status) {
    case "completed":
      return CheckCircle;
    case "inprogress":
      return Clock;
    default:
      return FileText;
  }
};

const getStatusColor = (status: Document["status"]) => {
  switch (status) {
    case "created":
      return "text-gray-600";
    case "submitted":
      return "text-blue-600";
    case "received":
      return "text-purple-600";
    case "negotiating":
      return "text-orange-600";
    case "agreed":
      return "text-green-600";
    case "inprogress":
      return "text-yellow-600";
    case "reviewing":
      return "text-indigo-600";
    case "completed":
      return "text-emerald-600";
    default:
      return "text-gray-600";
  }
};

export function DocumentDetailsContent({ document }: DocumentDetailsContentProps) {
  const StatusIcon = getStatusIcon(document.status);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Content */}
      <div className="lg:col-span-2 space-y-6">
        {/* Document Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Document Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">File Name</h3>
              <p className="text-gray-900">{document.name}</p>
            </div>

            <Separator />

            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">File Path</h3>
              <p className="text-gray-900 font-mono text-sm bg-gray-50 p-2 rounded">
                {document.path}
              </p>
            </div>

            <Separator />

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">File Type</h3>
                <p className="text-gray-900">{document.file_type}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">File Size</h3>
                <p className="text-gray-900">
                  {(parseInt(document.size) / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">Category</h3>
              <Badge variant="outline" className="capitalize">
                <Tag className="h-3 w-3 mr-1" />
                {document.category}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Association Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LinkIcon className="h-5 w-5" />
              Association Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Entity Type</h3>
                <p className="text-gray-900 capitalize">{document.association_entity}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Entity ID</h3>
                <p className="text-gray-900 font-mono text-sm">{document.association_id}</p>
              </div>
            </div>

            {document.proposalId && (
              <>
                <Separator />
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Proposal ID</h3>
                  <p className="text-gray-900 font-mono text-sm">{document.proposalId}</p>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <StatusIcon className={`h-5 w-5 ${getStatusColor(document.status)}`} />
              Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Badge
              variant="secondary"
              className={`${
                document.status === "completed"
                  ? "bg-emerald-100 text-emerald-800"
                  : document.status === "inprogress"
                  ? "bg-yellow-100 text-yellow-800"
                  : document.status === "reviewing"
                  ? "bg-indigo-100 text-indigo-800"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              {document.status}
            </Badge>
          </CardContent>
        </Card>

        {/* Timestamps */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Timeline
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">Created</h3>
              <p className="text-gray-900 text-sm">
                {format(new Date(document.createdAt), "PPP 'at' p")}
              </p>
            </div>

            <Separator />

            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">Last Updated</h3>
              <p className="text-gray-900 text-sm">
                {format(new Date(document.updatedAt), "PPP 'at' p")}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <button
              onClick={() => window.open(document.path, '_blank')}
              className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 transition-colors"
            >
              View Document
            </button>
            <button
              onClick={() => console.log('Download:', document.path)}
              className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 transition-colors"
            >
              Download File
            </button>
            <button
              onClick={() => console.log('Share:', document.id)}
              className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 transition-colors"
            >
              Share Document
            </button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
