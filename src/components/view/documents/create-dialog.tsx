"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/common/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { Upload, X } from "lucide-react";
import type { CreateDocument } from "@/lib/api/validators/schemas/document";

interface DocumentCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateDocument: (data: CreateDocument) => Promise<void>;
  isLoading: boolean;
}

export function DocumentCreateDialog({
  open,
  onOpenChange,
  onCreateDocument,
  isLoading,
}: DocumentCreateDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    path: "",
    file_type: "",
    size: "",
    category: "",
    association_entity: "",
    association_id: "",
    proposalId: "",
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setFormData((prev) => ({
        ...prev,
        name: file.name,
        path: `/documents/${file.name}`,
        file_type: file.type,
        size: file.size.toString(),
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFile) {
      alert("Please select a file");
      return;
    }

    try {
      await onCreateDocument({
        name: formData.name,
        path: formData.path,
        file_type: formData.file_type,
        size: formData.size,
        category: formData.category,
        association_entity: formData.association_entity,
        association_id: formData.association_id,
        proposalId: formData.proposalId || undefined,
      });

      // Reset form
      setFormData({
        name: "",
        path: "",
        file_type: "",
        size: "",
        category: "",
        association_entity: "",
        association_id: "",
        proposalId: "",
      });
      setSelectedFile(null);
    } catch (error) {
      console.error("Failed to create document:", error);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setFormData((prev) => ({
      ...prev,
      name: "",
      path: "",
      file_type: "",
      size: "",
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl flex flex-col gap-8">
        <DialogHeader>
          <DialogTitle>Upload New Document</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="file">File</Label>
            {!selectedFile ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Click to upload a file
                    </span>
                    <span className="mt-1 block text-sm text-gray-500">
                      or drag and drop
                    </span>
                  </label>
                  <input
                    id="file-upload"
                    type="file"
                    className="sr-only"
                    onChange={handleFileSelect}
                  />
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="text-sm">
                    <div className="font-medium text-gray-900">
                      {selectedFile.name}
                    </div>
                    <div className="text-gray-500">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveFile}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          {/* Document Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Document Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Enter document name"
              required
            />
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select
              value={formData.category}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, category: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="requirements">Requirements</SelectItem>
                <SelectItem value="specifications">Specifications</SelectItem>
                <SelectItem value="design">Design</SelectItem>
                <SelectItem value="documentation">Documentation</SelectItem>
                <SelectItem value="legal">Legal</SelectItem>
                <SelectItem value="testing">Testing</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Association Entity */}
          <div className="space-y-2">
            <Label htmlFor="association_entity">Association Entity</Label>
            <Select
              value={formData.association_entity}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, association_entity: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select entity type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="proposal">Proposal</SelectItem>
                <SelectItem value="contract">Contract</SelectItem>
                <SelectItem value="project">Project</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Association ID */}
          <div className="space-y-2">
            <Label htmlFor="association_id">Association ID</Label>
            <Input
              id="association_id"
              value={formData.association_id}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  association_id: e.target.value,
                }))
              }
              placeholder="Enter associated entity ID"
              required
            />
          </div>

          {/* Proposal ID (Optional) */}
          <div className="space-y-2">
            <Label htmlFor="proposalId">Proposal ID (Optional)</Label>
            <Input
              id="proposalId"
              value={formData.proposalId}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, proposalId: e.target.value }))
              }
              placeholder="Enter proposal ID if applicable"
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || !selectedFile}>
              {isLoading ? "Creating..." : "Create Document"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
