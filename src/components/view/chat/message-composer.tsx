"use client";

import React, { useState, useCallback, useRef, useEffect } from "react";
import dynamic from "next/dynamic";
import { But<PERSON> } from "@/components/common/ui/button";
import { Send, Paperclip, Smile } from "lucide-react";
import { cn } from "@/lib/utils";

const ReactQuill = dynamic(() => import("react-quill-new"), { ssr: false });
import "react-quill-new/dist/quill.snow.css";

interface MessageComposerProps {
  onSendMessage: (content: string) => void;
  onStartTyping?: () => void;
  onStopTyping?: () => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

// Simplified modules for chat
const chatModules = {
  toolbar: [
    ["bold", "italic", "underline"],
    ["link"],
    [{ list: "ordered" }, { list: "bullet" }],
    ["clean"],
  ],
  keyboard: {
    bindings: {
      enter: {
        key: 13,
        handler: function (this: any, range: any, context: any) {
          // Send message on Enter (without Shift)
          if (!context.shiftKey) {
            const composer = this.quill.container.closest(".message-composer");
            if (composer) {
              const sendButton = composer.querySelector("[data-send-button]");
              if (sendButton) {
                sendButton.click();
              }
            }
            return false;
          }
          // Allow line break on Shift+Enter
          return true;
        },
      },
    },
  },
};

const chatFormats = ["bold", "italic", "underline", "link", "list"];

export function MessageComposer({
  onSendMessage,
  onStartTyping,
  onStopTyping,
  disabled = false,
  placeholder = "Type your message...",
  className,
}: MessageComposerProps) {
  const [content, setContent] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const quillRef = useRef<any>();

  // Handle content change
  const handleChange = useCallback(
    (value: string) => {
      setContent(value);

      // Handle typing indicators
      if (value.trim() && !isTyping) {
        setIsTyping(true);
        onStartTyping?.();
      }

      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set new timeout to stop typing
      typingTimeoutRef.current = setTimeout(() => {
        setIsTyping(false);
        onStopTyping?.();
      }, 1000);
    },
    [isTyping, onStartTyping, onStopTyping]
  );

  // Handle send message
  const handleSend = useCallback(() => {
    const textContent = content.replace(/<[^>]*>/g, "").trim();

    if (!textContent || disabled) return;

    onSendMessage(content);
    setContent("");
    setIsTyping(false);
    onStopTyping?.();

    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Focus back to editor
    if (quillRef.current) {
      quillRef.current.focus();
    }
  }, [content, disabled, onSendMessage, onStopTyping]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  const isEmpty = !content.replace(/<[^>]*>/g, "").trim();

  return (
    <div className={cn("message-composer border-t bg-white p-4", className)}>
      <div className="flex flex-col gap-3">
        {/* Rich text editor */}
        <div className="relative">
          <ReactQuill
            ref={quillRef}
            value={content}
            onChange={handleChange}
            placeholder={placeholder}
            theme="snow"
            modules={chatModules}
            formats={chatFormats}
            style={{
              minHeight: "80px",
              maxHeight: "200px",
            }}
            readOnly={disabled}
          />
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              disabled={disabled}
            >
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              disabled={disabled}
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          <Button
            onClick={handleSend}
            disabled={disabled || isEmpty}
            size="sm"
            className="gap-2"
            data-send-button
          >
            <Send className="h-4 w-4" />
            Send
          </Button>
        </div>
      </div>

      {/* Custom styles for chat quill */}
      <style jsx global>{`
        .message-composer .ql-toolbar {
          border-top: none;
          border-left: none;
          border-right: none;
          border-bottom: 1px solid #e5e7eb;
          padding: 8px 12px;
        }

        .message-composer .ql-container {
          border: none;
          font-size: 14px;
        }

        .message-composer .ql-editor {
          padding: 12px;
          min-height: 60px;
          max-height: 150px;
          overflow-y: auto;
        }

        .message-composer .ql-editor.ql-blank::before {
          font-style: normal;
          color: #9ca3af;
        }
      `}</style>
    </div>
  );
}
